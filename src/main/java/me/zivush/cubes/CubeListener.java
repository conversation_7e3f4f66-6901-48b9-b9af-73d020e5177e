package me.zivush.cubes;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;

public class CubeListener implements Listener {
    private final Cubes plugin;
    private final ConfigManager configManager;
    private final CubeManager cubeManager;
    private final GuiManager guiManager;
    
    public CubeListener(Cubes plugin, ConfigManager configManager, CubeManager cubeManager, GuiManager guiManager) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.cubeManager = cubeManager;
        this.guiManager = guiManager;
    }
    
    @EventHandler
    public void onBlockPlace(BlockPlaceEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItemInHand();
        
        // Check if the item is a cube item
        String cubeType = getCubeTypeFromItem(item);
        if (cubeType != null) {
            event.setCancelled(true);
            
            Location location = event.getBlock().getLocation();
            if (cubeManager.placeCube(player, location, cubeType)) {
                // Remove one item from player's hand
                if (item.getAmount() > 1) {
                    item.setAmount(item.getAmount() - 1);
                } else {
                    player.getInventory().setItemInMainHand(null);
                }
            }
        }
    }
    
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        if (event.getAction().name().contains("RIGHT_CLICK_BLOCK")) {
            Player player = event.getPlayer();
            Block clickedBlock = event.getClickedBlock();
            
            if (clickedBlock != null) {
                String cubeId = findCubeByBorderBlock(clickedBlock.getLocation());
                if (cubeId != null) {
                    CubeData cubeData = cubeManager.getCubeData(cubeId);
                    if (cubeData != null) {
                        // Check if player is owner or has permission
                        if (cubeData.getOwner().equals(player.getUniqueId()) || player.hasPermission("cubes.admin")) {
                            if (player.isSneaking()) {
                                // Shift + right click = remove cube
                                cubeManager.removeCube(cubeId);
                                ItemStack cubeItem = cubeManager.createCubeItem(cubeData.getCubeType());
                                player.getInventory().addItem(cubeItem);
                                player.sendMessage(configManager.getMessage("messages.cube-removed"));
                            } else {
                                // Right click = open GUI
                                guiManager.openCubeGui(player, cubeId);
                            }
                            event.setCancelled(true);
                        } else {
                            player.sendMessage(configManager.getMessage("messages.not-owner"));
                            event.setCancelled(true);
                        }
                    }
                }
            }
        }
    }
    
    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Player player = event.getPlayer();
        Block block = event.getBlock();
        Location location = block.getLocation();
        
        // Check if the block is inside a cube
        String cubeId = findCubeContainingBlock(location);
        if (cubeId != null) {
            CubeData cubeData = cubeManager.getCubeData(cubeId);
            if (cubeData != null) {
                // Check if it's a border block - borders are unbreakable
                if (isBorderBlock(location, cubeData)) {
                    event.setCancelled(true);
                    player.sendMessage(configManager.getMessage("messages.cannot-break-border"));
                    return;
                }
                
                // Check if player is owner or has permission
                if (!cubeData.getOwner().equals(player.getUniqueId()) && !player.hasPermission("cubes.admin")) {
                    event.setCancelled(true);
                    player.sendMessage(configManager.getMessage("messages.not-owner"));
                    return;
                }
                
                // Add broken block to cube inventory instead of dropping
                Material material = block.getType();
                cubeData.addToInventory(material, 1);

                // Cancel the event to prevent normal drops
                event.setCancelled(true);

                // Set block to air
                block.setType(Material.AIR);

                // Optional: Send message only occasionally or remove entirely
                // player.sendMessage(configManager.getMessage("messages.block-added-to-cube",
                //     "{material}", material.name().toLowerCase().replace("_", " ")));
            }
        }
    }
    
    private String getCubeTypeFromItem(ItemStack item) {
        if (item == null || !item.hasItemMeta() || !item.getItemMeta().hasDisplayName()) {
            return null;
        }
        
        String displayName = item.getItemMeta().getDisplayName();
        
        // Check all cube types in config
        for (String cubeType : configManager.getConfig().getConfigurationSection("cubes").getKeys(false)) {
            String configName = configManager.getConfig().getString("cubes." + cubeType + ".item.name", "");
            configName = org.bukkit.ChatColor.translateAlternateColorCodes('&', configName);
            
            if (displayName.equals(configName)) {
                return cubeType;
            }
        }
        
        return null;
    }
    
    private String findCubeByBorderBlock(Location location) {
        for (String cubeId : cubeManager.getActiveCubes().keySet()) {
            CubeData cubeData = cubeManager.getCubeData(cubeId);
            if (cubeData != null && isBorderBlock(location, cubeData)) {
                return cubeId;
            }
        }
        return null;
    }
    
    private String findCubeContainingBlock(Location location) {
        for (String cubeId : cubeManager.getActiveCubes().keySet()) {
            CubeData cubeData = cubeManager.getCubeData(cubeId);
            if (cubeData != null && isBlockInsideCube(location, cubeData)) {
                return cubeId;
            }
        }
        return null;
    }
    
    private boolean isBorderBlock(Location location, CubeData cubeData) {
        Location cubeLocation = cubeData.getLocation();
        int size = configManager.getCubeSize(cubeData.getCubeType());
        
        int relX = location.getBlockX() - cubeLocation.getBlockX();
        int relY = location.getBlockY() - cubeLocation.getBlockY();
        int relZ = location.getBlockZ() - cubeLocation.getBlockZ();
        
        // Check if it's within the cube area (including borders)
        if (relX >= -1 && relX <= size && relY >= 0 && relY <= size + 1 && relZ >= -1 && relZ <= size) {
            // Check if it's actually a border
            return relX == -1 || relX == size || relY == 0 || relY == size + 1 || relZ == -1 || relZ == size;
        }
        
        return false;
    }
    
    private boolean isBlockInsideCube(Location location, CubeData cubeData) {
        Location cubeLocation = cubeData.getLocation();
        int size = configManager.getCubeSize(cubeData.getCubeType());
        
        int relX = location.getBlockX() - cubeLocation.getBlockX();
        int relY = location.getBlockY() - cubeLocation.getBlockY();
        int relZ = location.getBlockZ() - cubeLocation.getBlockZ();
        
        // Check if it's inside the cube (not including borders)
        return relX >= 0 && relX < size && relY >= 1 && relY <= size && relZ >= 0 && relZ < size;
    }
}
