package me.zivush.cubes;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.inventory.ItemStack;

import java.io.File;
import java.io.IOException;
import java.util.*;

public class CubeData {
    private final String cubeId;
    private final UUID owner;
    private final String cubeType;
    private final Location location;
    private final Map<Material, Integer> inventory;
    private final File dataFile;
    private YamlConfiguration config;
    
    public CubeData(String cubeId, UUID owner, String cubeType, Location location) {
        this.cubeId = cubeId;
        this.owner = owner;
        this.cubeType = cubeType;
        this.location = location;
        this.inventory = new HashMap<>();
        this.dataFile = new File(Cubes.getInstance().getDataFolder(), "data/" + cubeId + ".yml");
        this.config = YamlConfiguration.loadConfiguration(dataFile);
        loadData();
    }
    
    public CubeData(String cubeId) {
        this.cubeId = cubeId;
        this.dataFile = new File(Cubes.getInstance().getDataFolder(), "data/" + cubeId + ".yml");
        this.config = YamlConfiguration.loadConfiguration(dataFile);
        
        this.owner = UUID.fromString(config.getString("owner"));
        this.cubeType = config.getString("cube-type");
        this.location = new Location(
            Cubes.getInstance().getServer().getWorld(config.getString("location.world")),
            config.getDouble("location.x"),
            config.getDouble("location.y"),
            config.getDouble("location.z")
        );
        this.inventory = new HashMap<>();
        loadData();
    }
    
    private void loadData() {
        if (config.contains("inventory")) {
            for (String materialName : config.getConfigurationSection("inventory").getKeys(false)) {
                Material material = Material.valueOf(materialName);
                int amount = config.getInt("inventory." + materialName);
                inventory.put(material, amount);
            }
        }
    }
    
    public void saveData() {
        try {
            config.set("owner", owner.toString());
            config.set("cube-type", cubeType);
            config.set("location.world", location.getWorld().getName());
            config.set("location.x", location.getX());
            config.set("location.y", location.getY());
            config.set("location.z", location.getZ());
            
            config.set("inventory", null);
            for (Map.Entry<Material, Integer> entry : inventory.entrySet()) {
                config.set("inventory." + entry.getKey().name(), entry.getValue());
            }
            
            dataFile.getParentFile().mkdirs();
            config.save(dataFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    
    public void addToInventory(Material material, int amount) {
        inventory.put(material, inventory.getOrDefault(material, 0) + amount);
        saveData();
    }
    
    public boolean removeFromInventory(Material material, int amount) {
        int current = inventory.getOrDefault(material, 0);
        if (current >= amount) {
            if (current == amount) {
                inventory.remove(material);
            } else {
                inventory.put(material, current - amount);
            }
            saveData();
            return true;
        }
        return false;
    }
    
    public int getInventoryAmount(Material material) {
        return inventory.getOrDefault(material, 0);
    }
    
    public Map<Material, Integer> getInventory() {
        return new HashMap<>(inventory);
    }
    
    public void clearInventory() {
        inventory.clear();
        saveData();
    }
    
    public void delete() {
        if (dataFile.exists()) {
            dataFile.delete();
        }
    }
    
    // Getters
    public String getCubeId() { return cubeId; }
    public UUID getOwner() { return owner; }
    public String getCubeType() { return cubeType; }
    public Location getLocation() { return location; }
}
