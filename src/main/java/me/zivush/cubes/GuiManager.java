package me.zivush.cubes;

import net.milkbowl.vault.economy.Economy;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class GuiManager implements Listener {
    private final Cubes plugin;
    private final ConfigManager configManager;
    private final CubeManager cubeManager;
    private final Economy economy;
    
    public GuiManager(Cubes plugin, ConfigManager configManager, CubeManager cubeManager, Economy economy) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.cubeManager = cubeManager;
        this.economy = economy;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }
    
    public void openCubeGui(Player player, String cubeId) {
        CubeData cubeData = cubeManager.getCubeData(cubeId);
        if (cubeData == null) return;
        
        String title = configManager.getGuiConfig().getString("gui.title", "&6Cube Management");
        title = ChatColor.translateAlternateColorCodes('&', title);
        
        int size = configManager.getGuiConfig().getInt("gui.size", 54);
        Inventory gui = Bukkit.createInventory(null, size, title);
        
        // Fill inventory content slots
        fillInventoryContent(gui, cubeData);
        
        // Add buttons
        addButtons(gui, cubeData, player);
        
        player.openInventory(gui);
    }
    
    private void fillInventoryContent(Inventory gui, CubeData cubeData) {
        List<Integer> contentSlots = configManager.getGuiConfig().getIntegerList("gui.inventory-content.slots");
        Map<Material, Integer> inventory = cubeData.getInventory();
        
        int slotIndex = 0;
        for (Map.Entry<Material, Integer> entry : inventory.entrySet()) {
            if (slotIndex >= contentSlots.size()) break;
            
            Material material = entry.getKey();
            int amount = entry.getValue();
            
            if (amount > 0) {
                ItemStack item = new ItemStack(material, Math.min(amount, 64));
                ItemMeta meta = item.getItemMeta();
                if (meta != null) {
                    meta.setDisplayName(ChatColor.WHITE + material.name().toLowerCase().replace("_", " "));
                    List<String> lore = new ArrayList<>();
                    lore.add(ChatColor.GRAY + "Amount: " + ChatColor.WHITE + amount);
                    lore.add(ChatColor.GRAY + "Left click: Take 1");
                    lore.add(ChatColor.GRAY + "Right click: Take stack");
                    lore.add(ChatColor.GRAY + "Shift+Left click: Take all");
                    meta.setLore(lore);
                    item.setItemMeta(meta);
                }
                
                gui.setItem(contentSlots.get(slotIndex), item);
                slotIndex++;
            }
        }
    }
    
    private void addButtons(Inventory gui, CubeData cubeData, Player player) {
        String cubeType = cubeData.getCubeType();
        
        // Upgrade button
        String upgradeTo = configManager.getUpgradeTo(cubeType);
        if (upgradeTo != null) {
            int upgradeSlot = configManager.getGuiConfig().getInt("gui.buttons.upgrade.slot", 45);
            ItemStack upgradeButton = createButton("upgrade", cubeData);
            gui.setItem(upgradeSlot, upgradeButton);
        }
        
        // Rebuild button
        int rebuildSlot = configManager.getGuiConfig().getInt("gui.buttons.rebuild.slot", 46);
        ItemStack rebuildButton = createButton("rebuild", cubeData);
        gui.setItem(rebuildSlot, rebuildButton);
        
        // Smelt button
        int smeltSlot = configManager.getGuiConfig().getInt("gui.buttons.smelt.slot", 47);
        ItemStack smeltButton = createButton("smelt", cubeData);
        gui.setItem(smeltSlot, smeltButton);
        
        // Compress button
        int compressSlot = configManager.getGuiConfig().getInt("gui.buttons.compress.slot", 48);
        ItemStack compressButton = createButton("compress", cubeData);
        gui.setItem(compressSlot, compressButton);
        
        // Remove button
        int removeSlot = configManager.getGuiConfig().getInt("gui.buttons.remove.slot", 49);
        ItemStack removeButton = createButton("remove", cubeData);
        gui.setItem(removeSlot, removeButton);
    }
    
    private ItemStack createButton(String buttonType, CubeData cubeData) {
        String materialName = configManager.getGuiConfig().getString("gui.buttons." + buttonType + ".material", "STONE");
        Material material = Material.valueOf(materialName.toUpperCase());
        
        ItemStack button = new ItemStack(material);
        ItemMeta meta = button.getItemMeta();
        
        if (meta != null) {
            String name = configManager.getGuiConfig().getString("gui.buttons." + buttonType + ".name", "&c" + buttonType);
            meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', name));
            
            List<String> lore = configManager.getGuiConfig().getStringList("gui.buttons." + buttonType + ".lore");
            List<String> coloredLore = new ArrayList<>();
            double cost = getCost(buttonType, cubeData);

            for (String line : lore) {
                // Replace cost placeholders
                String costText = cost <= 0 ? "FREE" : String.format("%.2f", cost);
                String processedLine = line.replace("{cost}", costText);

                // Add additional placeholders for specific operations
                if (buttonType.equals("rebuild")) {
                    int missingBlocks = getMissingBlocksCount(cubeData);
                    processedLine = processedLine.replace("{missing_blocks}", String.valueOf(missingBlocks));
                } else if (buttonType.equals("smelt")) {
                    int smeltableItems = getSmeltableItemsCount(cubeData);
                    processedLine = processedLine.replace("{smeltable_items}", String.valueOf(smeltableItems));
                } else if (buttonType.equals("compress")) {
                    int compressableItems = getCompressableItemsCount(cubeData);
                    processedLine = processedLine.replace("{compressable_items}", String.valueOf(compressableItems));
                }

                coloredLore.add(ChatColor.translateAlternateColorCodes('&', processedLine));
            }
            
            meta.setLore(coloredLore);
            button.setItemMeta(meta);
        }
        
        return button;
    }
    
    private double getCost(String buttonType, CubeData cubeData) {
        String cubeType = cubeData.getCubeType();
        switch (buttonType) {
            case "upgrade":
                return configManager.getUpgradeCost(cubeType);
            case "rebuild":
                return calculateRebuildCost(cubeData);
            case "smelt":
                return calculateSmeltCost(cubeData);
            case "compress":
                return calculateCompressCost(cubeData);
            default:
                return 0.0;
        }
    }
    
    private double calculateRebuildCost(CubeData cubeData) {
        String cubeType = cubeData.getCubeType();
        int size = configManager.getCubeSize(cubeType);
        int totalBlocks = size * size * size;
        
        // Count missing blocks
        int missingBlocks = 0;
        for (int x = 0; x < size; x++) {
            for (int y = 1; y <= size; y++) {
                for (int z = 0; z < size; z++) {
                    if (cubeData.getLocation().clone().add(x, y, z).getBlock().getType() == Material.AIR) {
                        missingBlocks++;
                    }
                }
            }
        }
        
        return missingBlocks * configManager.getRebuildCost(cubeType);
    }
    
    private double calculateSmeltCost(CubeData cubeData) {
        String cubeType = cubeData.getCubeType();
        int smeltableItems = 0;
        
        for (Map.Entry<Material, Integer> entry : cubeData.getInventory().entrySet()) {
            if (isSmeltable(entry.getKey())) {
                smeltableItems += entry.getValue();
            }
        }
        
        return smeltableItems * configManager.getSmeltCost(cubeType);
    }
    
    private double calculateCompressCost(CubeData cubeData) {
        String cubeType = cubeData.getCubeType();
        int compressableItems = getCompressableItemsCount(cubeData);
        return compressableItems * configManager.getCompressCost(cubeType);
    }

    private int getMissingBlocksCount(CubeData cubeData) {
        String cubeType = cubeData.getCubeType();
        int size = configManager.getCubeSize(cubeType);
        int missingBlocks = 0;

        for (int x = 0; x < size; x++) {
            for (int y = 1; y <= size; y++) {
                for (int z = 0; z < size; z++) {
                    if (cubeData.getLocation().clone().add(x, y, z).getBlock().getType() == Material.AIR) {
                        missingBlocks++;
                    }
                }
            }
        }
        return missingBlocks;
    }

    private int getSmeltableItemsCount(CubeData cubeData) {
        int smeltableItems = 0;
        for (Map.Entry<Material, Integer> entry : cubeData.getInventory().entrySet()) {
            if (isSmeltable(entry.getKey())) {
                smeltableItems += entry.getValue();
            }
        }
        return smeltableItems;
    }

    private int getCompressableItemsCount(CubeData cubeData) {
        int compressableItems = 0;
        for (Map.Entry<Material, Integer> entry : cubeData.getInventory().entrySet()) {
            if (isCompressable(entry.getKey())) {
                compressableItems += entry.getValue() / 9;
            }
        }
        return compressableItems;
    }
    
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();
        
        String title = configManager.getGuiConfig().getString("gui.title", "&6Cube Management");
        title = ChatColor.translateAlternateColorCodes('&', title);
        
        if (!event.getView().getTitle().equals(title)) return;
        
        event.setCancelled(true);
        
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR) return;
        
        // Find cube data based on player location or other method
        // This is a simplified approach - in practice you'd need to track which GUI belongs to which cube
        String cubeId = findCubeIdForPlayer(player);
        if (cubeId == null) return;
        
        CubeData cubeData = cubeManager.getCubeData(cubeId);
        if (cubeData == null) return;
        
        handleGuiClick(player, event.getSlot(), event.getClick(), cubeData, clickedItem);
    }
    
    private String findCubeIdForPlayer(Player player) {
        // This is a simplified implementation
        // In practice, you'd track which GUI belongs to which cube
        for (Map.Entry<String, CubeData> entry : cubeManager.getActiveCubes().entrySet()) {
            if (entry.getValue().getOwner().equals(player.getUniqueId())) {
                return entry.getKey();
            }
        }
        return null;
    }
    
    private void handleGuiClick(Player player, int slot, ClickType clickType, CubeData cubeData, ItemStack clickedItem) {
        // Handle inventory content clicks
        List<Integer> contentSlots = configManager.getGuiConfig().getIntegerList("gui.inventory-content.slots");
        if (contentSlots.contains(slot)) {
            handleInventoryWithdraw(player, clickedItem, clickType, cubeData);
            return;
        }
        
        // Handle button clicks
        int upgradeSlot = configManager.getGuiConfig().getInt("gui.buttons.upgrade.slot", 45);
        int rebuildSlot = configManager.getGuiConfig().getInt("gui.buttons.rebuild.slot", 46);
        int smeltSlot = configManager.getGuiConfig().getInt("gui.buttons.smelt.slot", 47);
        int compressSlot = configManager.getGuiConfig().getInt("gui.buttons.compress.slot", 48);
        int removeSlot = configManager.getGuiConfig().getInt("gui.buttons.remove.slot", 49);
        
        if (slot == upgradeSlot) {
            handleUpgrade(player, cubeData);
        } else if (slot == rebuildSlot) {
            handleRebuild(player, cubeData);
        } else if (slot == smeltSlot) {
            handleSmelt(player, cubeData);
        } else if (slot == compressSlot) {
            handleCompress(player, cubeData);
        } else if (slot == removeSlot) {
            handleRemove(player, cubeData);
        }
    }
    
    private void handleInventoryWithdraw(Player player, ItemStack clickedItem, ClickType clickType, CubeData cubeData) {
        Material material = clickedItem.getType();
        int available = cubeData.getInventoryAmount(material);
        
        if (available <= 0) return;
        
        int toWithdraw = 1;
        if (clickType == ClickType.RIGHT) {
            toWithdraw = Math.min(available, 64);
        } else if (clickType == ClickType.SHIFT_LEFT) {
            toWithdraw = available;
        }
        
        if (cubeData.removeFromInventory(material, toWithdraw)) {
            ItemStack item = new ItemStack(material, toWithdraw);
            player.getInventory().addItem(item);
            openCubeGui(player, cubeData.getCubeId()); // Refresh GUI
        }
    }
    
    private void handleUpgrade(Player player, CubeData cubeData) {
        String cubeType = cubeData.getCubeType();
        String upgradeTo = configManager.getUpgradeTo(cubeType);
        double cost = configManager.getUpgradeCost(cubeType);

        if (upgradeTo == null) {
            player.sendMessage(configManager.getMessage("messages.upgrade-not-available"));
            return;
        }

        if (economy.getBalance(player) >= cost) {
            economy.withdrawPlayer(player, cost);

            // Upgrade the cube
            cubeManager.upgradeCube(cubeData.getCubeId(), upgradeTo);

            player.sendMessage(configManager.getMessage("messages.cube-upgraded",
                "{old_type}", cubeType, "{new_type}", upgradeTo));
            player.closeInventory();
        } else {
            player.sendMessage(configManager.getMessage("messages.insufficient-funds"));
        }
    }
    
    private void handleRebuild(Player player, CubeData cubeData) {
        double cost = calculateRebuildCost(cubeData);

        if (economy.getBalance(player) >= cost) {
            economy.withdrawPlayer(player, cost);

            // Rebuild the cube
            cubeManager.rebuildCube(cubeData.getCubeId());

            player.sendMessage(configManager.getMessage("messages.cube-rebuilt"));
            openCubeGui(player, cubeData.getCubeId()); // Refresh GUI
        } else {
            player.sendMessage(configManager.getMessage("messages.insufficient-funds"));
        }
    }
    
    private void handleSmelt(Player player, CubeData cubeData) {
        double cost = calculateSmeltCost(cubeData);
        
        if (economy.getBalance(player) >= cost) {
            economy.withdrawPlayer(player, cost);
            smeltItems(cubeData);
            player.sendMessage(configManager.getMessage("messages.items-smelted"));
            openCubeGui(player, cubeData.getCubeId()); // Refresh GUI
        } else {
            player.sendMessage(configManager.getMessage("messages.insufficient-funds"));
        }
    }
    
    private void handleCompress(Player player, CubeData cubeData) {
        double cost = calculateCompressCost(cubeData);
        
        if (economy.getBalance(player) >= cost) {
            economy.withdrawPlayer(player, cost);
            compressItems(cubeData);
            player.sendMessage(configManager.getMessage("messages.items-compressed"));
            openCubeGui(player, cubeData.getCubeId()); // Refresh GUI
        } else {
            player.sendMessage(configManager.getMessage("messages.insufficient-funds"));
        }
    }
    
    private void handleRemove(Player player, CubeData cubeData) {
        cubeManager.removeCube(cubeData.getCubeId());
        ItemStack cubeItem = cubeManager.createCubeItem(cubeData.getCubeType());
        player.getInventory().addItem(cubeItem);
        player.closeInventory();
        player.sendMessage(configManager.getMessage("messages.cube-removed"));
    }
    
    private boolean isSmeltable(Material material) {
        return material == Material.IRON_ORE || material == Material.GOLD_ORE ||
               material == Material.COPPER_ORE || material == Material.COBBLESTONE ||
               material == Material.SAND || material == Material.CLAY ||
               material == Material.NETHERRACK || material == Material.STONE ||
               material == Material.DEEPSLATE_IRON_ORE || material == Material.DEEPSLATE_GOLD_ORE ||
               material == Material.DEEPSLATE_COPPER_ORE || material == Material.DEEPSLATE_COAL_ORE ||
               material == Material.DEEPSLATE_DIAMOND_ORE || material == Material.DEEPSLATE_EMERALD_ORE ||
               material == Material.DEEPSLATE_LAPIS_ORE || material == Material.DEEPSLATE_REDSTONE_ORE ||
               material == Material.COAL_ORE || material == Material.DIAMOND_ORE ||
               material == Material.EMERALD_ORE || material == Material.LAPIS_ORE ||
               material == Material.REDSTONE_ORE || material == Material.NETHER_GOLD_ORE ||
               material == Material.NETHER_QUARTZ_ORE || material == Material.ANCIENT_DEBRIS ||
               material == Material.RAW_IRON || material == Material.RAW_GOLD ||
               material == Material.RAW_COPPER || material == Material.WET_SPONGE ||
               material == Material.CACTUS || material == Material.KELP ||
               material == Material.SEA_PICKLE || material == Material.CHORUS_FRUIT;
    }
    
    private boolean isCompressable(Material material) {
        return material == Material.IRON_INGOT || material == Material.GOLD_INGOT ||
               material == Material.COPPER_INGOT || material == Material.DIAMOND ||
               material == Material.EMERALD || material == Material.COAL;
    }
    
    private void smeltItems(CubeData cubeData) {
        Map<Material, Integer> inventory = cubeData.getInventory();
        for (Map.Entry<Material, Integer> entry : inventory.entrySet()) {
            Material material = entry.getKey();
            int amount = entry.getValue();
            
            if (isSmeltable(material) && amount > 0) {
                Material smelted = getSmeltedResult(material);
                if (smelted != null) {
                    cubeData.removeFromInventory(material, amount);
                    cubeData.addToInventory(smelted, amount);
                }
            }
        }
    }
    
    private void compressItems(CubeData cubeData) {
        Map<Material, Integer> inventory = cubeData.getInventory();
        for (Map.Entry<Material, Integer> entry : inventory.entrySet()) {
            Material material = entry.getKey();
            int amount = entry.getValue();
            
            if (isCompressable(material) && amount >= 9) {
                int blocks = amount / 9;
                Material compressed = getCompressedResult(material);
                if (compressed != null) {
                    cubeData.removeFromInventory(material, blocks * 9);
                    cubeData.addToInventory(compressed, blocks);
                }
            }
        }
    }
    
    private Material getSmeltedResult(Material material) {
        switch (material) {
            case IRON_ORE:
            case DEEPSLATE_IRON_ORE:
            case RAW_IRON: return Material.IRON_INGOT;
            case GOLD_ORE:
            case DEEPSLATE_GOLD_ORE:
            case NETHER_GOLD_ORE:
            case RAW_GOLD: return Material.GOLD_INGOT;
            case COPPER_ORE:
            case DEEPSLATE_COPPER_ORE:
            case RAW_COPPER: return Material.COPPER_INGOT;
            case COAL_ORE:
            case DEEPSLATE_COAL_ORE: return Material.COAL;
            case DIAMOND_ORE:
            case DEEPSLATE_DIAMOND_ORE: return Material.DIAMOND;
            case EMERALD_ORE:
            case DEEPSLATE_EMERALD_ORE: return Material.EMERALD;
            case LAPIS_ORE:
            case DEEPSLATE_LAPIS_ORE: return Material.LAPIS_LAZULI;
            case REDSTONE_ORE:
            case DEEPSLATE_REDSTONE_ORE: return Material.REDSTONE;
            case NETHER_QUARTZ_ORE: return Material.QUARTZ;
            case ANCIENT_DEBRIS: return Material.NETHERITE_SCRAP;
            case COBBLESTONE: return Material.STONE;
            case STONE: return Material.SMOOTH_STONE;
            case SAND: return Material.GLASS;
            case CLAY: return Material.BRICK;
            case NETHERRACK: return Material.NETHER_BRICK;
            case WET_SPONGE: return Material.SPONGE;
            case CACTUS: return Material.GREEN_DYE;
            case KELP: return Material.DRIED_KELP;
            case SEA_PICKLE: return Material.LIME_DYE;
            case CHORUS_FRUIT: return Material.POPPED_CHORUS_FRUIT;
            default: return null;
        }
    }
    
    private Material getCompressedResult(Material material) {
        switch (material) {
            case IRON_INGOT: return Material.IRON_BLOCK;
            case GOLD_INGOT: return Material.GOLD_BLOCK;
            case COPPER_INGOT: return Material.COPPER_BLOCK;
            case DIAMOND: return Material.DIAMOND_BLOCK;
            case EMERALD: return Material.EMERALD_BLOCK;
            case COAL: return Material.COAL_BLOCK;
            default: return null;
        }
    }
}
