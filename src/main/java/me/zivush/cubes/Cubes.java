package me.zivush.cubes;

import net.milkbowl.vault.economy.Economy;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.plugin.RegisteredServiceProvider;
import org.bukkit.plugin.java.JavaPlugin;

public final class Cubes extends JavaPlugin {

    private static Cubes instance;
    private ConfigManager configManager;
    private CubeManager cubeManager;
    private GuiManager guiManager;
    private Economy economy;

    @Override
    public void onEnable() {
        instance = this;

        // Setup Vault economy
        if (!setupEconomy()) {
            getLogger().severe("Vault economy not found! Disabling plugin.");
            getServer().getPluginManager().disablePlugin(this);
            return;
        }

        // Initialize managers
        configManager = new ConfigManager(this);
        cubeManager = new CubeManager(this, configManager);
        guiManager = new GuiManager(this, configManager, cubeManager, economy);

        // Register events
        CubeListener listener = new CubeListener(this, configManager, cubeManager, guiManager);
        getServer().getPluginManager().registerEvents(listener, this);

        getLogger().info("Cubes plugin has been enabled!");
    }

    @Override
    public void onDisable() {
        // Save all cube data
        if (cubeManager != null) {
            for (CubeData cubeData : cubeManager.getActiveCubes().values()) {
                cubeData.saveData();
            }
        }

        getLogger().info("Cubes plugin has been disabled!");
    }

    private boolean setupEconomy() {
        if (getServer().getPluginManager().getPlugin("Vault") == null) {
            return false;
        }
        RegisteredServiceProvider<Economy> rsp = getServer().getServicesManager().getRegistration(Economy.class);
        if (rsp == null) {
            return false;
        }
        economy = rsp.getProvider();
        return economy != null;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (command.getName().equalsIgnoreCase("cubes")) {
            if (args.length == 0) {
                sender.sendMessage(ChatColor.RED + "Usage: /cubes <give|reload> [player] [cube_type]");
                return true;
            }

            if (args[0].equalsIgnoreCase("reload")) {
                if (!sender.hasPermission("cubes.admin")) {
                    sender.sendMessage(configManager.getMessage("messages.no-permission"));
                    return true;
                }

                configManager.reloadConfigs();
                sender.sendMessage(configManager.getMessage("messages.config-reloaded"));
                return true;
            }

            if (args[0].equalsIgnoreCase("give")) {
                if (!sender.hasPermission("cubes.admin")) {
                    sender.sendMessage(configManager.getMessage("messages.no-permission"));
                    return true;
                }

                if (args.length < 3) {
                    sender.sendMessage(ChatColor.RED + "Usage: /cubes give <player> <cube_type>");
                    return true;
                }

                Player target = Bukkit.getPlayer(args[1]);
                if (target == null) {
                    sender.sendMessage(configManager.getMessage("messages.player-not-found"));
                    return true;
                }

                String cubeType = args[2];
                if (!configManager.getConfig().contains("cubes." + cubeType)) {
                    sender.sendMessage(configManager.getMessage("messages.invalid-cube-type"));
                    return true;
                }

                target.getInventory().addItem(cubeManager.createCubeItem(cubeType));
                sender.sendMessage(configManager.getMessage("messages.cube-given",
                    "{player}", target.getName(), "{cube_type}", cubeType));
                target.sendMessage(configManager.getMessage("messages.cube-received", "{cube_type}", cubeType));
                return true;
            }
        }

        return false;
    }

    public static Cubes getInstance() {
        return instance;
    }

    public ConfigManager getConfigManager() {
        return configManager;
    }

    public CubeManager getCubeManager() {
        return cubeManager;
    }

    public GuiManager getGuiManager() {
        return guiManager;
    }

    public Economy getEconomy() {
        return economy;
    }
}
