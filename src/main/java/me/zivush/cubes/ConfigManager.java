package me.zivush.cubes;

import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ConfigManager {
    private final Cubes plugin;
    private FileConfiguration config;
    private FileConfiguration guiConfig;
    private FileConfiguration messagesConfig;
    
    private File configFile;
    private File guiFile;
    private File messagesFile;
    
    public ConfigManager(Cubes plugin) {
        this.plugin = plugin;
        setupConfigs();
    }
    
    private void setupConfigs() {
        configFile = new File(plugin.getDataFolder(), "config.yml");
        guiFile = new File(plugin.getDataFolder(), "gui.yml");
        messagesFile = new File(plugin.getDataFolder(), "messages.yml");
        
        if (!configFile.exists()) {
            plugin.saveResource("config.yml", false);
        }
        if (!guiFile.exists()) {
            plugin.saveResource("gui.yml", false);
        }
        if (!messagesFile.exists()) {
            plugin.saveResource("messages.yml", false);
        }
        
        config = YamlConfiguration.loadConfiguration(configFile);
        guiConfig = YamlConfiguration.loadConfiguration(guiFile);
        messagesConfig = YamlConfiguration.loadConfiguration(messagesFile);
        
        // Load defaults
        InputStream defConfigStream = plugin.getResource("config.yml");
        if (defConfigStream != null) {
            YamlConfiguration defConfig = YamlConfiguration.loadConfiguration(new InputStreamReader(defConfigStream));
            config.setDefaults(defConfig);
        }
        
        InputStream defGuiStream = plugin.getResource("gui.yml");
        if (defGuiStream != null) {
            YamlConfiguration defGui = YamlConfiguration.loadConfiguration(new InputStreamReader(defGuiStream));
            guiConfig.setDefaults(defGui);
        }
        
        InputStream defMessagesStream = plugin.getResource("messages.yml");
        if (defMessagesStream != null) {
            YamlConfiguration defMessages = YamlConfiguration.loadConfiguration(new InputStreamReader(defMessagesStream));
            messagesConfig.setDefaults(defMessages);
        }
    }
    
    public void reloadConfigs() {
        config = YamlConfiguration.loadConfiguration(configFile);
        guiConfig = YamlConfiguration.loadConfiguration(guiFile);
        messagesConfig = YamlConfiguration.loadConfiguration(messagesFile);
    }
    
    public String getMessage(String path) {
        String message = messagesConfig.getString(path, "Message not found: " + path);
        return ChatColor.translateAlternateColorCodes('&', message);
    }
    
    public String getMessage(String path, String... replacements) {
        String message = getMessage(path);
        for (int i = 0; i < replacements.length; i += 2) {
            if (i + 1 < replacements.length) {
                message = message.replace(replacements[i], replacements[i + 1]);
            }
        }
        return message;
    }
    
    public List<String> getMessageList(String path) {
        List<String> messages = messagesConfig.getStringList(path);
        for (int i = 0; i < messages.size(); i++) {
            messages.set(i, ChatColor.translateAlternateColorCodes('&', messages.get(i)));
        }
        return messages;
    }
    
    public Map<Material, Double> getCubeMaterials(String cubeType) {
        Map<Material, Double> materials = new HashMap<>();
        if (config.contains("cubes." + cubeType + ".materials")) {
            for (String materialName : config.getConfigurationSection("cubes." + cubeType + ".materials").getKeys(false)) {
                Material material = Material.valueOf(materialName.toUpperCase());
                double percentage = config.getDouble("cubes." + cubeType + ".materials." + materialName);
                materials.put(material, percentage);
            }
        }
        return materials;
    }
    
    public Material getBorderMaterial(String cubeType) {
        String materialName = config.getString("cubes." + cubeType + ".border-material", "BEDROCK");
        return Material.valueOf(materialName.toUpperCase());
    }
    
    public int getCubeSize(String cubeType) {
        return config.getInt("cubes." + cubeType + ".size", 5);
    }
    
    public boolean hasRegeneration(String cubeType) {
        return config.getBoolean("cubes." + cubeType + ".regeneration.enabled", true);
    }
    
    public int getRegenerationTiming(String cubeType) {
        return config.getInt("cubes." + cubeType + ".regeneration.timing", 2400);
    }
    
    public double getRegenerationQuantity(String cubeType) {
        return config.getDouble("cubes." + cubeType + ".regeneration.quantity.value", 100.0);
    }
    
    public String getRegenerationType(String cubeType) {
        return config.getString("cubes." + cubeType + ".regeneration.type", "RANDOM");
    }
    
    public double getRebuildCost(String cubeType) {
        return config.getDouble("cubes." + cubeType + ".costs.rebuild", 5.0);
    }
    
    public double getSmeltCost(String cubeType) {
        return config.getDouble("cubes." + cubeType + ".costs.smelt", 1.0);
    }
    
    public double getCompressCost(String cubeType) {
        return config.getDouble("cubes." + cubeType + ".costs.compress", 0.0);
    }
    
    public String getUpgradeTo(String cubeType) {
        return config.getString("cubes." + cubeType + ".upgrade.to", null);
    }
    
    public double getUpgradeCost(String cubeType) {
        return config.getDouble("cubes." + cubeType + ".upgrade.cost", 0.0);
    }
    
    // Getters for configurations
    public FileConfiguration getConfig() { return config; }
    public FileConfiguration getGuiConfig() { return guiConfig; }
    public FileConfiguration getMessagesConfig() { return messagesConfig; }
}
