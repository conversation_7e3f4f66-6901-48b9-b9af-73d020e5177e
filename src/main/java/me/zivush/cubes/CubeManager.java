package me.zivush.cubes;

import com.mojang.authlib.GameProfile;
import com.mojang.authlib.properties.Property;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.File;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class CubeManager {
    private final Cubes plugin;
    private final ConfigManager configManager;
    private final Map<String, CubeData> activeCubes;
    private final Map<Location, String> cubeLocations;
    private final Map<String, BukkitRunnable> regenerationTasks;
    
    public CubeManager(Cubes plugin, ConfigManager configManager) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.activeCubes = new HashMap<>();
        this.cubeLocations = new HashMap<>();
        this.regenerationTasks = new HashMap<>();
        loadExistingCubes();
    }
    
    private void loadExistingCubes() {
        File dataFolder = new File(plugin.getDataFolder(), "data");
        if (!dataFolder.exists()) return;
        
        File[] files = dataFolder.listFiles((dir, name) -> name.endsWith(".yml"));
        if (files != null) {
            for (File file : files) {
                String cubeId = file.getName().replace(".yml", "");
                CubeData cubeData = new CubeData(cubeId);
                activeCubes.put(cubeId, cubeData);
                cubeLocations.put(cubeData.getLocation(), cubeId);
                startRegeneration(cubeData);
            }
        }
    }
    
    public ItemStack createCubeItem(String cubeType) {
        String materialName = configManager.getConfig().getString("cubes." + cubeType + ".item.material", "PLAYER_HEAD");
        Material material = Material.valueOf(materialName.toUpperCase());
        
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            String name = configManager.getConfig().getString("cubes." + cubeType + ".item.name", "&6" + cubeType + " Cube");
            meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', name));
            
            List<String> lore = configManager.getConfig().getStringList("cubes." + cubeType + ".item.lore");
            if (!lore.isEmpty()) {
                List<String> coloredLore = new ArrayList<>();
                for (String line : lore) {
                    coloredLore.add(ChatColor.translateAlternateColorCodes('&', line));
                }
                meta.setLore(coloredLore);
            }
            
            // Handle player head texture
            if (material == Material.PLAYER_HEAD && meta instanceof SkullMeta) {
                String texture = configManager.getConfig().getString("cubes." + cubeType + ".item.head-texture");
                if (texture != null && !texture.isEmpty()) {
                    setSkullTexture((SkullMeta) meta, texture);
                }
            }
            
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    private void setSkullTexture(SkullMeta skullMeta, String texture) {
        try {
            GameProfile profile = new GameProfile(UUID.randomUUID(), null);
            profile.getProperties().put("textures", new Property("textures", texture));
            
            Field profileField = skullMeta.getClass().getDeclaredField("profile");
            profileField.setAccessible(true);
            profileField.set(skullMeta, profile);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    public boolean placeCube(Player player, Location location, String cubeType) {
        int size = configManager.getCubeSize(cubeType);
        
        // Check if there's enough space
        if (!hasEnoughSpace(location, size)) {
            player.sendMessage(configManager.getMessage("messages.not-enough-space"));
            return false;
        }
        
        // Create cube ID
        String cubeId = UUID.randomUUID().toString();
        
        // Create cube data
        CubeData cubeData = new CubeData(cubeId, player.getUniqueId(), cubeType, location);
        activeCubes.put(cubeId, cubeData);
        cubeLocations.put(location, cubeId);
        
        // Build the cube
        buildCube(cubeData);
        
        // Start regeneration
        startRegeneration(cubeData);
        
        player.sendMessage(configManager.getMessage("messages.cube-placed"));
        return true;
    }
    
    private boolean hasEnoughSpace(Location location, int size) {
        for (int x = -1; x <= size; x++) {
            for (int y = 0; y <= size + 1; y++) {
                for (int z = -1; z <= size; z++) {
                    Block block = location.clone().add(x, y, z).getBlock();
                    if (block.getType() != Material.AIR) {
                        return false;
                    }
                }
            }
        }
        return true;
    }
    
    private void buildCube(CubeData cubeData) {
        String cubeType = cubeData.getCubeType();
        int size = configManager.getCubeSize(cubeType);
        Material borderMaterial = configManager.getBorderMaterial(cubeType);
        Map<Material, Double> materials = configManager.getCubeMaterials(cubeType);
        
        Location loc = cubeData.getLocation();
        
        // Build borders
        for (int x = -1; x <= size; x++) {
            for (int y = 0; y <= size + 1; y++) {
                for (int z = -1; z <= size; z++) {
                    boolean isBorder = x == -1 || x == size || y == 0 || y == size + 1 || z == -1 || z == size;
                    if (isBorder) {
                        loc.clone().add(x, y, z).getBlock().setType(borderMaterial);
                    }
                }
            }
        }
        
        // Fill inside with materials
        fillCubeWithMaterials(cubeData, materials);
    }
    
    private void fillCubeWithMaterials(CubeData cubeData, Map<Material, Double> materials) {
        String cubeType = cubeData.getCubeType();
        int size = configManager.getCubeSize(cubeType);
        Location loc = cubeData.getLocation();
        
        List<Material> materialList = new ArrayList<>();
        for (Map.Entry<Material, Double> entry : materials.entrySet()) {
            int count = (int) (entry.getValue() / 100.0 * size * size * size);
            for (int i = 0; i < count; i++) {
                materialList.add(entry.getKey());
            }
        }
        
        // Fill remaining with first material if needed
        while (materialList.size() < size * size * size) {
            materialList.add(materials.keySet().iterator().next());
        }
        
        Collections.shuffle(materialList);
        
        int index = 0;
        for (int x = 0; x < size; x++) {
            for (int y = 1; y <= size; y++) {
                for (int z = 0; z < size; z++) {
                    if (index < materialList.size()) {
                        loc.clone().add(x, y, z).getBlock().setType(materialList.get(index));
                        index++;
                    }
                }
            }
        }
    }
    
    public void removeCube(String cubeId) {
        CubeData cubeData = activeCubes.get(cubeId);
        if (cubeData == null) return;
        
        // Stop regeneration
        BukkitRunnable task = regenerationTasks.get(cubeId);
        if (task != null) {
            task.cancel();
            regenerationTasks.remove(cubeId);
        }
        
        // Clear blocks
        clearCubeBlocks(cubeData);
        
        // Remove data
        cubeData.delete();
        activeCubes.remove(cubeId);
        cubeLocations.remove(cubeData.getLocation());
    }
    
    private void clearCubeBlocks(CubeData cubeData) {
        String cubeType = cubeData.getCubeType();
        int size = configManager.getCubeSize(cubeType);
        Location loc = cubeData.getLocation();
        
        for (int x = -1; x <= size; x++) {
            for (int y = 0; y <= size + 1; y++) {
                for (int z = -1; z <= size; z++) {
                    loc.clone().add(x, y, z).getBlock().setType(Material.AIR);
                }
            }
        }
    }
    
    private void startRegeneration(CubeData cubeData) {
        String cubeType = cubeData.getCubeType();
        if (!configManager.hasRegeneration(cubeType)) return;
        
        int timing = configManager.getRegenerationTiming(cubeType);
        
        BukkitRunnable task = new BukkitRunnable() {
            @Override
            public void run() {
                regenerateBlocks(cubeData);
            }
        };
        
        task.runTaskTimer(plugin, timing, timing);
        regenerationTasks.put(cubeData.getCubeId(), task);
    }
    
    private void regenerateBlocks(CubeData cubeData) {
        String cubeType = cubeData.getCubeType();
        double quantity = configManager.getRegenerationQuantity(cubeType);
        String type = configManager.getRegenerationType(cubeType);
        Map<Material, Double> materials = configManager.getCubeMaterials(cubeType);
        
        int size = configManager.getCubeSize(cubeType);
        Location loc = cubeData.getLocation();
        
        List<Location> airBlocks = new ArrayList<>();
        for (int x = 0; x < size; x++) {
            for (int y = 1; y <= size; y++) {
                for (int z = 0; z < size; z++) {
                    Location blockLoc = loc.clone().add(x, y, z);
                    if (blockLoc.getBlock().getType() == Material.AIR) {
                        airBlocks.add(blockLoc);
                    }
                }
            }
        }
        
        if (airBlocks.isEmpty()) return;
        
        int blocksToRegenerate = (int) (airBlocks.size() * quantity / 100.0);
        
        if (type.equals("RANDOM")) {
            Collections.shuffle(airBlocks);
        }
        
        for (int i = 0; i < Math.min(blocksToRegenerate, airBlocks.size()); i++) {
            Material material = getRandomMaterial(materials);
            airBlocks.get(i).getBlock().setType(material);
        }
    }
    
    private Material getRandomMaterial(Map<Material, Double> materials) {
        double totalWeight = materials.values().stream().mapToDouble(Double::doubleValue).sum();
        double random = ThreadLocalRandom.current().nextDouble() * totalWeight;
        
        double currentWeight = 0;
        for (Map.Entry<Material, Double> entry : materials.entrySet()) {
            currentWeight += entry.getValue();
            if (random <= currentWeight) {
                return entry.getKey();
            }
        }
        
        return materials.keySet().iterator().next();
    }
    
    // Getters
    public CubeData getCubeData(String cubeId) { return activeCubes.get(cubeId); }
    public String getCubeIdAtLocation(Location location) { return cubeLocations.get(location); }
    public Map<String, CubeData> getActiveCubes() { return new HashMap<>(activeCubes); }
}
