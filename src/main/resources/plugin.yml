name: Cubes
version: '${project.version}'
main: me.zivush.cubes.Cubes
api-version: '1.20'
author: Zivush
description: A customizable cube mining plugin with GUI management
depend: [Vault]

commands:
  cubes:
    description: Main cubes command
    usage: /cubes <give|reload> [player] [cube_type]
    permission: cubes.admin
    aliases: [cube]

permissions:
  cubes.admin:
    description: Access to all cube commands
    default: op
  cubes.use:
    description: Use cubes and access GUI
    default: true
