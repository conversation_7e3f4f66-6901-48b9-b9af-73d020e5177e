# ================================
#       MESSAGES CONFIGURATION
# ================================
# Customize all plugin messages
# Supports color codes with & symbol
# Use {placeholders} for dynamic content

# General Messages
messages:
  # Plugin prefix for all messages
  prefix: "&8[&6Cubes&8] "
  
  # Permission Messages
  no-permission: "&cYou don't have permission to use this command!"
  not-owner: "&cYou don't own this cube!"
  
  # Command Messages
  config-reloaded: "&aConfiguration files have been reloaded successfully!"
  player-not-found: "&cPlayer not found or not online!"
  invalid-cube-type: "&cInvalid cube type! Check your configuration."
  
  # Cube Placement Messages
  cube-placed: "&aYour cube has been placed successfully!"
  not-enough-space: "&cNot enough space to place the cube here!"
  max-cubes-reached: "&cYou have reached the maximum number of cubes allowed!"
  too-close-to-other-cube: "&cThis location is too close to another cube!"
  world-not-allowed: "&cCubes are not allowed in this world!"
  
  # Cube Management Messages
  cube-removed: "&aCube removed successfully! The cube item has been returned to your inventory."
  cube-given: "&aCube '{cube_type}' has been given to {player}!"
  cube-received: "&aYou have received a {cube_type} cube!"
  
  # Mining Messages
  block-added-to-cube: "&7{material} has been added to your cube inventory."
  cannot-break-border: "&cYou cannot break the cube border!"
  
  # GUI Operation Messages
  cube-upgraded: "&aYour cube has been upgraded successfully!"
  cube-rebuilt: "&aYour cube has been rebuilt! All missing blocks have been regenerated."
  items-smelted: "&aAll smeltable items have been processed!"
  items-compressed: "&aAll compressible items have been compressed!"
  
  # Economy Messages
  insufficient-funds: "&cYou don't have enough money for this operation!"
  cost-deducted: "&7${amount} has been deducted from your account."
  
  # Error Messages
  cube-not-found: "&cCube not found!"
  operation-failed: "&cOperation failed! Please try again."
  database-error: "&cDatabase error occurred. Please contact an administrator."
  
  # Success Messages
  operation-successful: "&aOperation completed successfully!"
  
  # Inventory Messages
  inventory-full: "&cYour inventory is full! Some items may have been dropped."
  item-withdrawn: "&7You withdrew {amount}x {material} from your cube."
  no-items-to-withdraw: "&cNo items of this type in your cube!"
  
  # Upgrade Messages
  upgrade-not-available: "&cNo upgrade available for this cube type!"
  upgrade-requirements-not-met: "&cYou don't meet the requirements for this upgrade!"
  
  # Regeneration Messages
  regeneration-started: "&aBlock regeneration has started for your cube."
  regeneration-completed: "&aBlock regeneration completed! {blocks} blocks were regenerated."
  
  # Maintenance Messages
  plugin-reloading: "&ePlugin is reloading... Please wait."
  maintenance-mode: "&cThe plugin is currently in maintenance mode."
  
  # Help Messages
  help-header: "&6&l=== Cubes Plugin Help ==="
  help-commands:
    - "&e/cubes give <player> <type> &7- Give a cube to a player"
    - "&e/cubes reload &7- Reload configuration files"
  help-usage:
    - "&6&lHow to use cubes:"
    - "&7• Right-click to place a cube"
    - "&7• Right-click border to open GUI"
    - "&7• Shift+Right-click border to remove"
    - "&7• Mine blocks inside to collect them"
  help-footer: "&6&l=========================="

# Action Bar Messages (optional)
actionbar:
  mining-progress: "&7Mining progress: &a{percentage}% &7complete"
  regeneration-timer: "&7Next regeneration in: &e{time}"
  cube-info: "&7Cube: &6{type} &7| Owner: &e{owner}"

# Title Messages (optional)
titles:
  cube-placed:
    title: "&a&lCube Placed!"
    subtitle: "&7Your {type} cube is ready to mine"
    fade-in: 10
    stay: 40
    fade-out: 10
  
  cube-upgraded:
    title: "&6&lCube Upgraded!"
    subtitle: "&7Your cube is now a {new_type} cube"
    fade-in: 10
    stay: 40
    fade-out: 10
  
  level-up:
    title: "&e&lLevel Up!"
    subtitle: "&7Your cube has reached level {level}"
    fade-in: 10
    stay: 40
    fade-out: 10

# Hologram Messages (if hologram support is added)
holograms:
  cube-info:
    - "&6&l{type} Cube"
    - "&7Owner: &e{owner}"
    - "&7Level: &a{level}"
    - "&7Efficiency: &b{efficiency}%"
  
  upgrade-available:
    - "&a&lUpgrade Available!"
    - "&7Click to upgrade to {next_type}"
    - "&7Cost: &6${cost}"

# Chat Format Messages
chat:
  broadcast-cube-placed: "&a{player} has placed a {type} cube!"
  broadcast-cube-upgraded: "&6{player} upgraded their cube to {type}!"
  broadcast-rare-drop: "&d{player} found a rare {item} in their cube!"

# Debug Messages (for development)
debug:
  cube-data-loaded: "&7[DEBUG] Loaded cube data for {cube_id}"
  regeneration-triggered: "&7[DEBUG] Regeneration triggered for cube {cube_id}"
  economy-transaction: "&7[DEBUG] Economy transaction: {amount} for {player}"
