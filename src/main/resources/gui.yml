# ================================
#         GUI CONFIGURATION
# ================================
# Customize the cube management GUI
# Design your interface with custom items and layouts

gui:
  # GUI Title (supports color codes with &)
  title: "&6&lCube Management"
  
  # GUI Size (must be multiple of 9, max 54)
  size: 54
  
  # Inventory Content Slots
  # These slots will display the cube's inventory items
  inventory-content:
    slots:
      - 10
      - 11
      - 12
      - 13
      - 14
      - 15
      - 16
      - 19
      - 20
      - 21
      - 22
      - 23
      - 24
      - 25
      - 28
      - 29
      - 30
      - 31
      - 32
      - 33
      - 34
  
  # Button Configurations
  buttons:
    # Upgrade Button
    upgrade:
      slot: 45
      material: EMERALD
      name: "&a&lUpgrade Cube"
      lore:
        - "&7Upgrade your cube to the next tier"
        - "&7for better materials and efficiency!"
        - ""
        - "&eClick to upgrade"
        - "&8(Only available if upgrade exists)"
    
    # Rebuild Button
    rebuild:
      slot: 46
      material: GRASS_BLOCK
      name: "&2&lRebuild Cube"
      lore:
        - "&7Regenerate all missing blocks"
        - "&7inside your cube instantly!"
        - ""
        - "&eClick to rebuild"
        - "&8Cost based on missing blocks"
    
    # Smelt Button
    smelt:
      slot: 47
      material: FURNACE
      name: "&6&lSmelt Items"
      lore:
        - "&7Automatically smelt all"
        - "&7smeltable items in your cube!"
        - ""
        - "&7Examples:"
        - "&8• Iron Ore → Iron Ingot"
        - "&8• Gold Ore → Gold Ingot"
        - "&8• Cobblestone → Stone"
        - ""
        - "&eClick to smelt"
        - "&8Cost per item smelted"
    
    # Compress Button
    compress:
      slot: 48
      material: PISTON
      name: "&9&lCompress Items"
      lore:
        - "&7Compress 9 items into blocks"
        - "&7to save inventory space!"
        - ""
        - "&7Examples:"
        - "&8• 9 Iron Ingots → 1 Iron Block"
        - "&8• 9 Gold Ingots → 1 Gold Block"
        - "&8• 9 Diamonds → 1 Diamond Block"
        - ""
        - "&eClick to compress"
        - "&8May have a cost per compression"
    
    # Remove Button
    remove:
      slot: 49
      material: TNT
      name: "&c&lRemove Cube"
      lore:
        - "&7Permanently remove this cube"
        - "&7and get the cube item back."
        - ""
        - "&c&lWARNING:"
        - "&cAll items in the cube inventory"
        - "&cwill be lost!"
        - ""
        - "&eClick to remove"
        - "&8Or Shift+Right-click the border"
  
  # Decorative Items (optional)
  decorations:
    # Glass panes for decoration
    glass-panes:
      enabled: true
      material: GRAY_STAINED_GLASS_PANE
      name: " "  # Empty name
      slots:
        - 0
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 7
        - 8
        - 9
        - 17
        - 18
        - 26
        - 27
        - 35
        - 36
        - 37
        - 38
        - 39
        - 40
        - 41
        - 42
        - 43
        - 44
        - 50
        - 51
        - 52
        - 53
    
    # Info item
    info:
      enabled: true
      slot: 4
      material: BOOK
      name: "&e&lCube Information"
      lore:
        - "&7This is your cube management interface."
        - "&7Here you can:"
        - ""
        - "&8• &7Withdraw mined items"
        - "&8• &7Upgrade your cube"
        - "&8• &7Rebuild missing blocks"
        - "&8• &7Smelt and compress items"
        - "&8• &7Remove the cube"
        - ""
        - "&6&lHow to withdraw items:"
        - "&8• &7Left Click: Take 1 item"
        - "&8• &7Right Click: Take 1 stack"
        - "&8• &7Shift+Left Click: Take all"

# Animation Settings (optional)
animations:
  # Enable GUI animations
  enabled: true
  
  # Opening animation
  opening:
    type: "FADE_IN"  # FADE_IN, SLIDE_DOWN, NONE
    duration: 10  # ticks
  
  # Button hover effects
  hover-effects:
    enabled: true
    glow: true
    sound: "UI_BUTTON_CLICK"

# Sound Effects
sounds:
  # Sound when opening GUI
  open: "BLOCK_CHEST_OPEN"
  
  # Sound when clicking buttons
  button-click: "UI_BUTTON_CLICK"
  
  # Sound when withdrawing items
  item-withdraw: "ENTITY_ITEM_PICKUP"
  
  # Sound for successful operations
  success: "ENTITY_PLAYER_LEVELUP"
  
  # Sound for failed operations
  error: "ENTITY_VILLAGER_NO"
