# ================================
#         CUBES CONFIGURATION
# ================================
# A fully customizable cube mining plugin
# Configure your cubes, materials, costs, and more!

# Cube Definitions
# Each cube type can have different materials, sizes, costs, and upgrades
cubes:
  # Basic Stone Cube
  stone:
    # Size of the cube (excluding borders)
    size: 5
    
    # Border material (the frame around the cube)
    border-material: BEDROCK
    
    # Materials inside the cube with their percentages
    # Percentages should add up to 100 or close to it
    materials:
      STONE: 40.0
      COBBLESTONE: 30.0
      COAL_ORE: 15.0
      IRON_ORE: 10.0
      COPPER_ORE: 5.0
    
    # Item representation of the cube
    item:
      material: PLAYER_HEAD
      name: "&7Stone Cube"
      lore:
        - "&8A basic mining cube"
        - "&8Contains stone and basic ores"
        - ""
        - "&eRight-click to place"
      # Base64 texture value for custom head (option 1)
      head-texture: "eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvYjc4ZWYyZTRjZjU4ZjEyNzU0OGY2Yzg4YjY4MjVjNzU3YWY5YzEzZTM1NzQ4NzVhNzE2ZDI5NzI4NTExNiJ9fX0="
      # OR direct URL to texture (option 2) - comment out head-texture if using this
      # head-url: "http://textures.minecraft.net/texture/b78ef2e4cf58f127548f6c88b6825c757af9c13e3574875a716d297285116"
    
    # Regeneration settings
    regeneration:
      enabled: true
      timing: 2400  # Ticks (2400 = 2 minutes)
      quantity:
        type: PERCENTAGE
        value: 50.0  # Regenerate 50% of missing blocks
      type: RANDOM  # RANDOM or ORDER
    
    # Costs for various operations
    costs:
      rebuild: 5.0    # Cost per missing block to rebuild
      smelt: 1.0      # Cost per item to smelt
      compress: 0.5   # Cost per compression
    
    # Upgrade settings (optional)
    upgrade:
      to: "iron"      # Upgrade to iron cube
      cost: 1000.0    # Cost to upgrade

  # Iron Cube (upgraded from stone)
  iron:
    size: 6
    border-material: IRON_BLOCK
    materials:
      IRON_ORE: 35.0
      GOLD_ORE: 20.0
      COAL_ORE: 20.0
      DIAMOND_ORE: 10.0
      EMERALD_ORE: 5.0
      STONE: 10.0
    
    item:
      material: PLAYER_HEAD
      name: "&fIron Cube"
      lore:
        - "&8An advanced mining cube"
        - "&8Contains valuable ores"
        - ""
        - "&eRight-click to place"
      head-texture: "eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvNzFiYzJiY2ZiMmJkMzc1OWU2YjFlODZmYzdhNzk1ODVlMTEyN2RkMzU3ZmMyMDI4OTNmOGZkZmZlZTk5YzUifX19"
    
    regeneration:
      enabled: true
      timing: 1800  # 1.5 minutes
      quantity:
        type: PERCENTAGE
        value: 75.0
      type: RANDOM
    
    costs:
      rebuild: 8.0
      smelt: 2.0
      compress: 1.0
    
    upgrade:
      to: "diamond"
      cost: 5000.0

  # Diamond Cube (premium tier)
  diamond:
    size: 8
    border-material: DIAMOND_BLOCK
    materials:
      DIAMOND_ORE: 25.0
      EMERALD_ORE: 20.0
      GOLD_ORE: 20.0
      IRON_ORE: 15.0
      NETHERITE_SCRAP: 5.0
      ANCIENT_DEBRIS: 5.0
      COAL_ORE: 10.0
    
    item:
      material: PLAYER_HEAD
      name: "&bDiamond Cube"
      lore:
        - "&8The ultimate mining cube"
        - "&8Contains the rarest materials"
        - ""
        - "&eRight-click to place"
      head-texture: "eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvNTBjMWIzODRmMTBhZDZhZmJkZGY3YWNiMTVkNjI2YTBmYTg2ZjE5ZDI5NWM4YzQyNGJlMDZhNzc5YWE4ZGEzIn19fQ=="
    
    regeneration:
      enabled: true
      timing: 1200  # 1 minute
      quantity:
        type: PERCENTAGE
        value: 100.0
      type: RANDOM
    
    costs:
      rebuild: 15.0
      smelt: 5.0
      compress: 2.0
    
    # No upgrade available (max tier)

  # Custom Nether Cube
  nether:
    size: 7
    border-material: OBSIDIAN
    materials:
      NETHERRACK: 30.0
      NETHER_QUARTZ_ORE: 25.0
      NETHER_GOLD_ORE: 20.0
      ANCIENT_DEBRIS: 10.0
      BLACKSTONE: 10.0
      BASALT: 5.0
    
    item:
      material: PLAYER_HEAD
      name: "&4Nether Cube"
      lore:
        - "&8A fiery mining cube"
        - "&8Contains nether materials"
        - ""
        - "&eRight-click to place"
      head-texture: "eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvMzM5NmNlOGZhZjJkMGIzZTk1ZjM0ZjJkZGRmMjU5YzRkNDI0ZjEyZmM4YWVkYTQ4YWJkNjNhZGZmNTJhOCJ9fX0="
    
    regeneration:
      enabled: true
      timing: 1600
      quantity:
        type: PERCENTAGE
        value: 60.0
      type: RANDOM
    
    costs:
      rebuild: 12.0
      smelt: 3.0
      compress: 1.5

# General Settings
settings:
  # Maximum cubes per player (0 = unlimited)
  max-cubes-per-player: 3
  
  # Allow cubes in specific worlds only
  allowed-worlds:
    - "world"
    - "world_nether"
    - "world_the_end"
  
  # Minimum distance between cubes
  min-distance-between-cubes: 10
  
  # Auto-save interval (in ticks)
  auto-save-interval: 6000  # 5 minutes
